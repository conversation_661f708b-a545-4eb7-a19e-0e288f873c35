//
//  AssociatedPersonsController.swift
//  
//
//  Created by Augment Agent on 6/30/25.
//

import Foundation
import Fluent
import Vapor

struct AssociatedPersonsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let associatedPersons = routes.grouped("members", ":memberID", "associated-persons")
        
        associatedPersons.get(use: index)
        associatedPersons.post(use: create)
        associatedPersons.group(":associatedPersonID") { associatedPerson in
            associatedPerson.get(use: show)
            associatedPerson.put(use: update)
            associatedPerson.delete(use: delete)
        }
    }
    
    // MARK: - CRUD Operations
    
    func index(req: Request) async throws -> [AssociatedPerson] {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        
        // Verify member exists
        guard let _ = try await Member.find(memberID, on: req.db) else {
            throw Abort(.notFound, reason: "Member not found")
        }
        
        return try await AssociatedPerson.query(on: req.db)
            .filter(\.$member.$id == memberID)
            .sort(\.$createdAt, .descending)
            .all()
    }
    
    func show(req: Request) async throws -> AssociatedPerson {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let associatedPersonID = try req.parameters.require("associatedPersonID", as: UUID.self)
        
        guard let associatedPerson = try await AssociatedPerson.query(on: req.db)
            .filter(\.$id == associatedPersonID)
            .filter(\.$member.$id == memberID)
            .first() else {
            throw Abort(.notFound, reason: "Associated person not found")
        }
        
        return associatedPerson
    }
    
    func create(req: Request) async throws -> AssociatedPerson {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let input = try req.content.decode(AssociatedPersonCreateInput.self)
        
        // Verify member exists
        guard let member = try await Member.find(memberID, on: req.db) else {
            throw Abort(.notFound, reason: "Member not found")
        }
        
        // Validate input
        try validateInput(input)
        
        let associatedPerson = AssociatedPerson(
            fullName: input.fullName,
            relationship: input.relationship,
            role: input.role,
            phone: input.phone,
            email: input.email,
            notes: input.notes,
            isPrimaryContact: input.isPrimaryContact,
            isEmergencyContact: input.isEmergencyContact
        )
        
        associatedPerson.$member.id = memberID
        
        try await associatedPerson.save(on: req.db)
        
        // Create timeline entry
        try await createTimelineEntry(
            operation: .created,
            associatedPerson: associatedPerson,
            member: member,
            on: req.db,
            req: req
        )
        
        return associatedPerson
    }
    
    func update(req: Request) async throws -> AssociatedPerson {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let associatedPersonID = try req.parameters.require("associatedPersonID", as: UUID.self)
        let input = try req.content.decode(AssociatedPersonUpdateInput.self)
        
        guard let associatedPerson = try await AssociatedPerson.query(on: req.db)
            .filter(\.$id == associatedPersonID)
            .filter(\.$member.$id == memberID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "Associated person not found")
        }
        
        // Update fields if provided
        if let fullName = input.fullName {
            associatedPerson.fullName = fullName
        }
        if let relationship = input.relationship {
            associatedPerson.relationship = relationship
        }
        if let role = input.role {
            associatedPerson.role = role
        }
        if let phone = input.phone {
            associatedPerson.phone = phone
        }
        if let email = input.email {
            associatedPerson.email = email
        }
        if let notes = input.notes {
            associatedPerson.notes = notes
        }
        if let isPrimaryContact = input.isPrimaryContact {
            associatedPerson.isPrimaryContact = isPrimaryContact
        }
        if let isEmergencyContact = input.isEmergencyContact {
            associatedPerson.isEmergencyContact = isEmergencyContact
        }
        
        try await associatedPerson.save(on: req.db)
        
        // Create timeline entry
        try await createTimelineEntry(
            operation: .updated,
            associatedPerson: associatedPerson,
            member: associatedPerson.member,
            on: req.db,
            req: req
        )
        
        return associatedPerson
    }
    
    func delete(req: Request) async throws -> HTTPStatus {
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let associatedPersonID = try req.parameters.require("associatedPersonID", as: UUID.self)
        
        guard let associatedPerson = try await AssociatedPerson.query(on: req.db)
            .filter(\.$id == associatedPersonID)
            .filter(\.$member.$id == memberID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "Associated person not found")
        }
        
        // Create timeline entry before deletion
        try await createTimelineEntry(
            operation: .deleted,
            associatedPerson: associatedPerson,
            member: associatedPerson.member,
            on: req.db,
            req: req
        )
        
        try await associatedPerson.delete(on: req.db)
        
        return .noContent
    }
    
    // MARK: - Helper Methods
    
    private func validateInput(_ input: AssociatedPersonCreateInput) throws {
        if input.fullName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw Abort(.badRequest, reason: "Full name is required")
        }
        
        if input.relationship.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            throw Abort(.badRequest, reason: "Relationship is required")
        }
        
        // Validate email format if provided
        if let email = input.email, !email.isEmpty {
            let emailRegex = #"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$"#
            if !NSPredicate(format: "SELF MATCHES %@", emailRegex).evaluate(with: email) {
                throw Abort(.badRequest, reason: "Invalid email format")
            }
        }
        
        // Validate phone format if provided (basic validation)
        if let phone = input.phone, !phone.isEmpty {
            let phoneRegex = #"^[\+]?[1-9][\d]{0,15}$"#
            let cleanPhone = phone.replacingOccurrences(of: "[^0-9+]", with: "", options: .regularExpression)
            if !NSPredicate(format: "SELF MATCHES %@", phoneRegex).evaluate(with: cleanPhone) {
                throw Abort(.badRequest, reason: "Invalid phone format")
            }
        }
    }

    // MARK: - Timeline Integration

    private func createTimelineEntry(
        operation: TimelineOperation,
        associatedPerson: AssociatedPerson,
        member: Member,
        on db: Database,
        req: Request
    ) async throws {
        let userID = try await TokenController.userIdFromToken(req: req)
        let associatedPersonID = try associatedPerson.requireID()

        let timelineItem = TimelineItem(
            carepackageID: "associated-person-\(associatedPersonID.uuidString)",
            status: operation.status,
            desc: operation.description(for: "Associated Person", details: "\(associatedPerson.fullName) (\(associatedPerson.relationship)) - \(associatedPerson.role.rawValue)"),
            title: operation.title(for: "Associated Person"),
            memberId: member.id,
            visible: true,
            meta: MetaData(data: [
                "ref_id": associatedPersonID.uuidString,
                "entity_type": "AssociatedPerson",
                "operation": operation.rawValue,
                "full_name": associatedPerson.fullName,
                "relationship": associatedPerson.relationship,
                "role": associatedPerson.role.rawValue
            ])
        )

        timelineItem.$creator.id = userID
        try await timelineItem.save(on: db)
    }
}
